package org.jeecg.modules.cw.statistics.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.log4j.Log4j2;
import org.jeecg.modules.cw.base.constants.CwCllDataName;
import org.jeecg.modules.cw.base.enums.CwBaseKeyName;
import org.jeecg.modules.cw.base.service.ICwCllDataService;
import org.jeecg.modules.cw.base.service.ICwKBaseService;
import org.jeecg.modules.cw.base.service.IDtEmployeeCountByDepartmentService;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.jeecg.modules.cw.krb.entity.CwKrbRow;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrStatisticsDayService;
import org.jeecg.modules.cw.statistics.entity.CwStatistics;
import org.jeecg.modules.cw.statistics.mapper.CwStatisticsMapper;
import org.jeecg.modules.cw.statistics.result.*;
import org.jeecg.modules.cw.statistics.service.ICwStatisticsService;
import org.jeecg.modules.cw.jlfx.service.ICwJlfxMonthService;
import org.jeecg.modules.cw.jlfx.result.CwJlfxQueryResult;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxRow;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.math.RoundingMode;

/**
 * @Description: 其他费用
 * @Author: jeecg-boot
 * @Date: 2025-01-02
 * @Version: V1.0
 */
@Service
@Log4j2
public class CwStatisticsServiceImpl extends ServiceImpl<CwStatisticsMapper, CwStatistics> implements ICwStatisticsService {

    // 按类型
    private final String TYPE_CL = "cl";
    private final String TYPE_BJ = "bj";
    private final String TYPE_RL = "rl";
    private final String TYPE_GZ = "gz";
    private final String TYPE_ZZFY = "zzfy";
    private final String TYPE_DL = "dl";
    // 按单位
    private final String FRDW = "frdw";
    private final String CKC = "ckc";
    private final String DS = "ds";
    private final String SX = "sx";
    private final String JW = "jw";
    private final String XJS = "xjs";
    private final String JH = "jh";

    @Resource
    private ICwMnlrDayService mnlrDayService;
    @Resource
    private ICwMnlrStatisticsDayService mnlrStatisticsDayService;
    @Resource
    private ICwKBaseService kBaseService;
    @Resource
    private ICwJlfxMonthService jlfxMonthService;
    @Resource
    private ICwCkcZhcbService ckcZhcbService;
    @Resource
    private ICwDsZhcbService dsZhcbService;
    @Resource
    private ICwSxZhcbService sxZhcbService;
    @Resource
    private ICwJwZhcbService jwZhcbService;
    @Resource
    private ICwCllDataService cwCllDataService;

    /** 人员统计服务 */
    @Resource
    private IDtEmployeeCountByDepartmentService employeeCountService;

    /**
     * 查询指定日期所在月份从1号到该日期的金属价格
     * @param date 当天日期
     * @return List<CwMetalPriceResult>
     */
    @Override
    public List<CwMetalPriceResult> getMetalPriceMonth(Date date) {
    	if (date == null) {
    		date = new Date();
    	}
   
    	// 计算开始、结束日期
    	Date startDate = DateUtil.beginOfMonth(date);
    	Date endDate = DateUtil.endOfDay(date);
    	long dayCount = DateUtil.betweenDay(startDate, endDate, true) + 1;
   
   
    	// 需要的type列表
    	List<String> types = Arrays.asList("t", "j", "y", "m", "ljk");
   
    	// 查询数据
    	List<CwMnlrDay> records = mnlrDayService.lambdaQuery()
    			.in(CwMnlrDay::getType, types)
    			.between(CwMnlrDay::getRecordTime, startDate, endDate)
    			.orderByAsc(CwMnlrDay::getRecordTime)
    			.list();
   
    	// 以日期为key进行分组
    	Map<String, List<CwMnlrDay>> dateGroup = new HashMap<>();
    	for (CwMnlrDay item : records) {
    		String dateKey = DateUtil.formatDate(item.getRecordTime());
    		dateGroup.computeIfAbsent(dateKey, k -> new ArrayList<>()).add(item);
    	}
   
    	// 组装结果，保证日期连续
    	List<CwMetalPriceResult> resultList = new ArrayList<>();
    	for (int i = 0; i < dayCount; i++) {
    		Date current = DateUtil.offsetDay(startDate, i);
    		String key = DateUtil.formatDate(current);
    		List<CwMnlrDay> dayList = dateGroup.getOrDefault(key, Collections.emptyList());
   
    		// 构建结果对象
    		CwMetalPriceResult res = new CwMetalPriceResult();
    		res.setDate(key);
   
    		// 默认0
    		res.setCu(BigDecimal.ZERO);
    		res.setAu(BigDecimal.ZERO);
    		res.setAg(BigDecimal.ZERO);
    		res.setMo(BigDecimal.ZERO);
    		res.setLjk(BigDecimal.ZERO);
   
    		for (CwMnlrDay d : dayList) {
    			if (d.getJg() == null) continue;
    			switch (d.getType()) {
    				case "t":
    					res.setCu(d.getJg());
    					break;
    				case "j":
    					res.setAu(d.getJg());
    					break;
    				case "y":
    					res.setAg(d.getJg());
    					break;
    				case "m":
    					res.setMo(d.getJg());
    					break;
    				case "ljk":
    					res.setLjk(d.getJg());
    					break;
    			}
    		}
    		resultList.add(res);
    	}
    	return resultList;
    }

    @Override
    public CwProfitStatResult profitStatistics(Date date, String dimension) {
        if (date == null) {
            date = new Date();
        }
        String dim = (dimension == null ? "day" : dimension.toLowerCase());

        CwProfitStatResult result = new CwProfitStatResult();
        result.setDimension(dim);
        // period 字符串
        switch (dim) {
            case "year":
                result.setPeriod(DateUtil.format(date, "yyyy"));
                calculateYearStatistics(date, result);
                break;
            case "month":
                result.setPeriod(DateUtil.format(date, "yyyy-MM"));
                calculateMonthStatistics(date, result);
                break;
            default:
                // 默认按天
                result.setPeriod(DateUtil.formatDate(date));
                calculateDayStatistics(date, result);
                break;
        }
        return result;
    }

    /** 日维度统计 */
    private void calculateDayStatistics(Date date, CwProfitStatResult result) {
        BigDecimal profit = mnlrStatisticsDayService.getDayProfit(date);
        result.setProfit(profit);

        // 环比：昨日
        BigDecimal prevProfit = mnlrStatisticsDayService.getDayProfit(DateUtil.offsetDay(date, -1));
        result.setHb(profit.subtract(prevProfit));

        // 同比：去年同日
        BigDecimal lastYearProfit = mnlrStatisticsDayService.getDayProfit(DateUtil.offset(date, cn.hutool.core.date.DateField.YEAR, -1));
        result.setTb(profit.subtract(lastYearProfit));

        // 计划比：日计划（年度计划 / 当前年份天数）
        BigDecimal plan = BigDecimal.ZERO;
        try {
            String planStr = kBaseService.getCwBaseDataYear(CwBaseKeyName.Year_JH, date);
            if (ObjectUtil.isNotEmpty(planStr)) {
                BigDecimal yearPlan = new BigDecimal(planStr);
                int year = DateUtil.year(date);
                int daysInYear = cn.hutool.core.date.DateUtil.isLeapYear(year) ? 366 : 365;
                plan = yearPlan.divide(new BigDecimal(daysInYear), 2, RoundingMode.HALF_UP);
            }
        } catch (Exception ignored) {
        }
        result.setJhb(profit.subtract(plan));
    }

    /** 月维度统计 */
    private void calculateMonthStatistics(Date date, CwProfitStatResult result) {
        BigDecimal profit = sumProfitRange(DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date));
        result.setProfit(profit);

        // 环比：上月
        Date prevMonth = DateUtil.offsetMonth(date, -1);
        BigDecimal prevProfit = sumProfitRange(DateUtil.beginOfMonth(prevMonth), DateUtil.endOfMonth(prevMonth));
        result.setHb(profit.subtract(prevProfit));

        // 同比：去年同月
        Date lastYear = DateUtil.offset(date, cn.hutool.core.date.DateField.YEAR, -1);
        BigDecimal lastYearProfit = sumProfitRange(DateUtil.beginOfMonth(lastYear), DateUtil.endOfMonth(lastYear));
        result.setTb(profit.subtract(lastYearProfit));

        // 计划比：月计划（年度计划 / 12）
        BigDecimal plan = BigDecimal.ZERO;
        try {
            String planStr = kBaseService.getCwBaseDataYear(CwBaseKeyName.Year_JH, date);
            if (ObjectUtil.isNotEmpty(planStr)) {
                BigDecimal yearPlan = new BigDecimal(planStr);
                plan = yearPlan.divide(new BigDecimal("12"), 2, RoundingMode.HALF_UP);
            }
        } catch (Exception ignored) {
        }
        result.setJhb(profit.subtract(plan));
    }

    /** 年维度统计（年累计） */
    private void calculateYearStatistics(Date date, CwProfitStatResult result) {
        BigDecimal profit = sumProfitRange(DateUtil.beginOfYear(date), DateUtil.endOfDay(date));
        result.setProfit(profit);

        // 年维度无环比
        result.setHb(null);

        // 同比：去年年累计（到去年同一日）
        Date lastYearEnd = DateUtil.offset(date, cn.hutool.core.date.DateField.YEAR, -1);
        BigDecimal lastYearProfit = sumProfitRange(DateUtil.beginOfYear(lastYearEnd), DateUtil.endOfDay(lastYearEnd));
        result.setTb(profit.subtract(lastYearProfit));

        // 计划比：年计划
        BigDecimal plan = BigDecimal.ZERO;
        try {
            String planStr = kBaseService.getCwBaseDataYear(CwBaseKeyName.Year_JH, date);
            if (ObjectUtil.isNotEmpty(planStr)) {
                plan = new BigDecimal(planStr);
            }
        } catch (Exception ignored) {
        }
        result.setJhb(profit.subtract(plan));
    }

    /**
     * 计算区间内(包含边界)所有日利润之和
     */
    private BigDecimal sumProfitRange(Date start, Date end) {
        return mnlrStatisticsDayService.sumMnlrRange(start, end);
    }

    @Override
    public List<CwProfitTrendPointResult> profitTrend(Date date, String dimension) {
        if (date == null) {
            date = new Date();
        }
        String dim = dimension == null ? "day" : dimension.toLowerCase();
        List<CwProfitTrendPointResult> list = new ArrayList<>();
        if ("month".equals(dim)) {
            // 从1月到当前月
            Date firstDayOfYear = DateUtil.beginOfYear(date);
            int monthCount = DateUtil.month(date) + 1; // hutool month 0-based
            for (int i = 0; i < monthCount; i++) {
                Date monthDate = DateUtil.offsetMonth(firstDayOfYear, i);
                Date start = DateUtil.beginOfMonth(monthDate);
                Date end = DateUtil.endOfMonth(monthDate);
                BigDecimal profit = mnlrStatisticsDayService.sumMnlrRange(start, end);
                CwProfitTrendPointResult point = new CwProfitTrendPointResult();
                point.setPeriod(DateUtil.format(monthDate, "yyyy-MM"));
                point.setProfit(profit);
                list.add(point);
            }
        } else {
            // day 维度：当月1号到指定日期
            Date startDate = DateUtil.beginOfMonth(date);
            long dayCount = DateUtil.betweenDay(startDate, date, true) + 1;
            for (int i = 0; i < dayCount; i++) {
            	Date d = DateUtil.offsetDay(startDate, i);
            	BigDecimal profit = mnlrStatisticsDayService.getDayProfit(d);
            	CwProfitTrendPointResult point = new CwProfitTrendPointResult();
            	point.setPeriod(DateUtil.formatDate(d));
            	point.setProfit(profit);
            	list.add(point);
            }
        }
        return list;
    }

    /**
     * 价量分析-金属利润柱状图数据
     */
    @Override
    public List<CwMetalProfitBarResult> metalProfitBar(Date date, String dimension) {
        if (date == null) {
            date = new Date();
        }
        String dim = dimension == null ? "month" : dimension.toLowerCase();

        // Map: metal key -> result holder (priceImpact, volumeImpact)
        Map<String, CwMetalProfitBarResult> resultMap = new LinkedHashMap<>();
        List<String> metalKeys = Arrays.asList("t", "j", "y", "ljk", "m");
        for (String k : metalKeys) {
            CwMetalProfitBarResult r = new CwMetalProfitBarResult();
            r.setMetal(k);
            r.setPriceImpact(BigDecimal.ZERO);
            r.setVolumeImpact(BigDecimal.ZERO);
            r.setPlan(BigDecimal.ZERO);
            r.setActual(BigDecimal.ZERO);
            resultMap.put(k, r);
        }

        if ("year".equals(dim)) {
            Date iter = DateUtil.beginOfYear(date);
            while (!iter.after(date)) {
                accumulateMetalImpactForMonth(iter, resultMap);
                iter = DateUtil.offsetMonth(iter, 1);
            }
        } else {
            accumulateMetalImpactForMonth(date, resultMap);
        }

        return new ArrayList<>(resultMap.values());
    }

    /**
     * 统计指定月份各金属售价/销量差异对利润的影响，并累加到 resultMap
     */
    private void accumulateMetalImpactForMonth(Date monthDate, Map<String, CwMetalProfitBarResult> map) {
        CwJlfxQueryResult qr = jlfxMonthService.queryByDate(monthDate);
        if (qr == null || qr.getRows() == null) return;

        Map<String, CwJlfxRow> priceRowMap = new HashMap<>();
        Map<String, CwJlfxRow> volumeRowMap = new HashMap<>();
        for (CwJlfxRow row : qr.getRows()) {
            if (row.getNameKey() == null) continue;
            if ("sj".equals(row.getType())) {
                priceRowMap.put(row.getNameKey(), row);
            } else if ("xl".equals(row.getType())) {
                volumeRowMap.put(row.getNameKey(), row);
            }
        }

        for (String key : map.keySet()) {
            CwJlfxRow priceRow = priceRowMap.get(key);
            CwJlfxRow volumeRow = volumeRowMap.get(key);
            if (priceRow == null || volumeRow == null) continue;

            BigDecimal actualPrice = nullSafe(priceRow.getSj());
            BigDecimal planPrice = nullSafe(priceRow.getJh());
            BigDecimal actualVol = nullSafe(volumeRow.getSj());
            BigDecimal planVol = nullSafe(volumeRow.getJh());

            ImpactConfig cfg = IMPACT_CONFIG.getOrDefault(key, ImpactConfig.DEFAULT);

            BigDecimal actualPriceNt = cfg.withoutTax ? actualPrice.divide(new BigDecimal("1.13"), 8, RoundingMode.HALF_UP) : actualPrice;
            BigDecimal planPriceNt = cfg.withoutTax ? planPrice.divide(new BigDecimal("1.13"), 8, RoundingMode.HALF_UP) : planPrice;

            BigDecimal priceDiff = actualPriceNt.subtract(planPriceNt);
            BigDecimal priceImpact = priceDiff.multiply(actualVol)
                    .divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP)
                    .multiply(cfg.coefficient);

            BigDecimal volumeDiff = actualVol.subtract(planVol);
            BigDecimal volumeImpact = volumeDiff.multiply(planPriceNt)
                    .divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP)
                    .multiply(cfg.coefficient);

            // 计算计划/实际利润
            BigDecimal planProfit = calcProfit(planPrice, planVol);
            BigDecimal actualProfit = calcProfit(actualPrice, actualVol);

            CwMetalProfitBarResult res = map.get(key);
            res.setPriceImpact(res.getPriceImpact().add(priceImpact));
            res.setVolumeImpact(res.getVolumeImpact().add(volumeImpact));
            res.setPlan(res.getPlan().add(planProfit));
            res.setActual(res.getActual().add(actualProfit));
            // 可同时更新 metal 显示名
            res.setMetal(priceRow.getName());
        }
    }

    /** 配置 */
    private static class ImpactConfig {
        BigDecimal coefficient;
        boolean withoutTax;
        ImpactConfig(String coef, boolean wt) { this.coefficient = new BigDecimal(coef); this.withoutTax = wt; }
        static final ImpactConfig DEFAULT = new ImpactConfig("1", true);
    }

    private static final Map<String, ImpactConfig> IMPACT_CONFIG = new HashMap<>();
    static {
        IMPACT_CONFIG.put("t", new ImpactConfig("0.9", true));
        IMPACT_CONFIG.put("j", new ImpactConfig("0.84", false));
        IMPACT_CONFIG.put("y", new ImpactConfig("0.75", true));
        IMPACT_CONFIG.put("ljk", new ImpactConfig("1", true));
        IMPACT_CONFIG.put("m", new ImpactConfig("1", true));
    }

    /**
     * 利润计算公式： price/1.13 * volume * 0.9 / 10000 （万元）
     */
    private BigDecimal calcProfit(BigDecimal price, BigDecimal volume) {
        if (price == null || volume == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal profit = price
                .divide(new BigDecimal("1.13"), 8, RoundingMode.HALF_UP)
                .multiply(volume)
                .multiply(new BigDecimal("0.9"))
                .divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP);
        return profit;
    }

    private BigDecimal nullSafe(BigDecimal v) {
        return v == null ? BigDecimal.ZERO : v;
    }

    @Override
    public CwCostStatResult costStatistics(Date date, String dimension) {
        if (date == null) {
            date = new Date();
        }
        String dim = dimension == null ? "month" : dimension.toLowerCase();
        CwCostStatResult result = new CwCostStatResult();
        result.setDimension(dim);
        BigDecimal totalCost = java.math.BigDecimal.ZERO;
        BigDecimal oreCll = java.math.BigDecimal.ZERO;

        if ("year".equals(dim)) {
            // 年累计：从1月到当前月
            Date iter = DateUtil.beginOfYear(date);
            while (!iter.after(date)) {
                totalCost = totalCost.add(monthTotalCost(iter));
                iter = DateUtil.offsetMonth(iter, 1);
            }
            // 处理量
            Date start = DateUtil.beginOfYear(date);
            Date end = DateUtil.endOfDay(date);
            Map<String, String> cllMap = cwCllDataService.getRangeCllData(CwCllDataName.CBZL, start, end);
            oreCll = sumStringMap(cllMap);
            result.setPeriod(DateUtil.format(date, "yyyy"));
        } else { // 默认month
            totalCost = monthTotalCost(date);
            Map<String, String> cllMap = cwCllDataService.getMonthCllData(CwCllDataName.CBZL, date);
            oreCll = sumStringMap(cllMap);
            result.setPeriod(DateUtil.format(date, "yyyy-MM"));
        }

        result.setTotalCost(totalCost);
        if (oreCll != null && oreCll.compareTo(java.math.BigDecimal.ZERO) > 0) {
            result.setTonCost(totalCost.divide(oreCll, 4, java.math.RoundingMode.HALF_UP));
        } else {
            result.setTonCost(java.math.BigDecimal.ZERO);
        }
        // 金属成本 = 总成本 / 金属量合计 ？？
        BigDecimal metalQty;
        if ("year".equals(dim)) {
            metalQty = sumMetalQuantity(DateUtil.beginOfYear(date), DateUtil.endOfDay(date));
        } else {
            // month 维度
            metalQty = sumMetalQuantity(DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date));
        }
        BigDecimal metalCost;
        if (metalQty != null && metalQty.compareTo(BigDecimal.ZERO) > 0) {
            metalCost = totalCost.divide(metalQty, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("10000"));
        } else {
            metalCost = BigDecimal.ZERO;
        }
        result.setMetalCost(metalCost);

        // 获取总产值和总利润
        BigDecimal totalOutput;
        BigDecimal totalProfit;
        if ("year".equals(dim)) {
            totalOutput = mnlrStatisticsDayService.sumSlRange(DateUtil.beginOfYear(date), DateUtil.endOfDay(date));
            totalProfit = mnlrStatisticsDayService.sumMnlrRange(DateUtil.beginOfYear(date), DateUtil.endOfDay(date));
        } else {
            totalOutput = mnlrStatisticsDayService.sumSlRange(DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date));
            totalProfit = mnlrStatisticsDayService.sumMnlrRange(DateUtil.beginOfMonth(date), DateUtil.endOfMonth(date));
        }

        // 获取过滤后的人员数量（排除新技术、铸造、实业、化工、铜兴监测）
        Integer empTotal;
        if ("year".equals(dim)) {
            // 年维度：取整年所有有效数据的平均值
            empTotal = employeeCountService.getFilteredEmployeeCountForYear(
                    DateUtil.beginOfYear(date),
                    DateUtil.endOfDay(date));
        } else {
            // 月维度：取当月任意一天的人数总和
            empTotal = employeeCountService.getFilteredEmployeeCountForMonth(
                    DateUtil.beginOfMonth(date),
                    DateUtil.endOfMonth(date));
        }

        // 计算人均产值和人均利润
        BigDecimal avgOutput;
        BigDecimal avgProfit;
        if (empTotal != null && empTotal > 0) {
            avgOutput = totalOutput.divide(new BigDecimal(empTotal), 4, RoundingMode.HALF_UP);
            avgProfit = totalProfit.divide(new BigDecimal(empTotal), 4, RoundingMode.HALF_UP);
        } else {
            avgOutput = BigDecimal.ZERO;
            avgProfit = BigDecimal.ZERO;
        }
        result.setAvgOutput(avgOutput);
        result.setAvgProfit(avgProfit);
        return result;
    }

    /**
     * 计算某月份的总成本（所有单位）
     */
    private BigDecimal monthTotalCost(Date monthDate) {
        return kBaseService.getKzcb(DateUtil.endOfMonth(monthDate));
    }

    /**
     * 统计区间内的金属量（销量 xl 之和）
     */
    private BigDecimal sumMetalQuantity(Date start, Date end) {
        List<String> metalTypes = Arrays.asList("t", "j", "y", "ljk", "m");
        List<CwMnlrDay> rows = mnlrDayService.lambdaQuery()
                .in(CwMnlrDay::getType, metalTypes)
                .between(CwMnlrDay::getRecordTime, start, end)
                .select(CwMnlrDay::getXl)
                .list();
        BigDecimal sum = BigDecimal.ZERO;
        if (rows != null) {
            for (CwMnlrDay r : rows) {
                if (r != null && r.getXl() != null) {
                    sum = sum.add(r.getXl());
                }
            }
        }
        return sum;
    }

    private java.math.BigDecimal safe(java.math.BigDecimal v) {
        return v == null ? java.math.BigDecimal.ZERO : v;
    }

    /**
     * 将字符串map累加为 BigDecimal 之和
     */
    private java.math.BigDecimal sumStringMap(java.util.Map<String, String> map) {
        java.math.BigDecimal sum = java.math.BigDecimal.ZERO;
        if (map != null) {
            for (String s : map.values()) {
                if (cn.hutool.core.util.ObjectUtil.isNotEmpty(s)) {
                    try {
                        sum = sum.add(new java.math.BigDecimal(s));
                    } catch (Exception ignored) {
                    }
                }
            }
        }
        return sum;
    }

    @Override
    public java.util.List<org.jeecg.modules.cw.statistics.result.CwCostTrendPointResult> costTrend(java.util.Date date) {
        if (date == null) {
            date = new java.util.Date();
        }
        java.util.List<org.jeecg.modules.cw.statistics.result.CwCostTrendPointResult> list = new java.util.ArrayList<>();
        java.util.Date firstDayOfYear = cn.hutool.core.date.DateUtil.beginOfYear(date);
        int monthCount = cn.hutool.core.date.DateUtil.month(date) + 1; // 0-based
        for (int i = 0; i < monthCount; i++) {
            java.util.Date monthDate = cn.hutool.core.date.DateUtil.offsetMonth(firstDayOfYear, i);
            java.math.BigDecimal totalCost = monthTotalCost(monthDate);
            java.util.Map<String, String> cllMap = cwCllDataService.getMonthCllData(org.jeecg.modules.cw.base.constants.CwCllDataName.SZCLL, monthDate);
            java.math.BigDecimal oreCll = sumStringMap(cllMap);
            java.math.BigDecimal tonCost;
            if (oreCll != null && oreCll.compareTo(java.math.BigDecimal.ZERO) > 0) {
                tonCost = totalCost.divide(oreCll, 4, java.math.RoundingMode.HALF_UP);
            } else {
                tonCost = java.math.BigDecimal.ZERO;
            }
            org.jeecg.modules.cw.statistics.result.CwCostTrendPointResult point = new org.jeecg.modules.cw.statistics.result.CwCostTrendPointResult();
            point.setPeriod(cn.hutool.core.date.DateUtil.format(monthDate, "yyyy-MM"));
            point.setTotalCost(totalCost);
            point.setTonCost(tonCost);
            point.setMetalCost(totalCost); // 暂同总成本
            list.add(point);
        }
        return list;
    }

    @Override
    public List<CwUnitCostBarResult> unitCostBar(Date date, String dimension) {
        if (date == null) {
            date = new Date();
        }
        String dim = (dimension == null ? "month" : dimension.toLowerCase());
        List<CwUnitCostBarResult> list = new ArrayList<>();

        // 固定顺序
        List<String> unitKeys = Arrays.asList("ckc", "ds", "sx", "jw");
        for (String key : unitKeys) {
            list.add(calcUnitCostBar(key, date, dim));
        }
        return list;
    }

    /**
     * 计算单个单位的成本柱状图数据
     */
    private CwUnitCostBarResult calcUnitCostBar(String unitKey, Date date, String dim) {
        BigDecimal actualTotal = BigDecimal.ZERO;
        BigDecimal planTotal = BigDecimal.ZERO;
        BigDecimal volumeTotal = BigDecimal.ZERO;

        if ("year".equals(dim)) {
            // 年累计：从1月到当前月
            Date iter = DateUtil.beginOfYear(date);
            while (!iter.after(date)) {
                UnitMonthlyData md = fetchUnitMonthlyData(unitKey, iter);
                actualTotal = actualTotal.add(md.actual);
                planTotal = planTotal.add(md.plan);
                volumeTotal = volumeTotal.add(md.volume);
                // 下一月
                iter = DateUtil.offsetMonth(iter, 1);
            }
        } else {
            // 默认 month
            UnitMonthlyData md = fetchUnitMonthlyData(unitKey, date);
            actualTotal = md.actual;
            planTotal = md.plan;
            volumeTotal = md.volume;
        }

        BigDecimal actualTon;
        BigDecimal planTon;
        if (volumeTotal != null && volumeTotal.compareTo(BigDecimal.ZERO) > 0) {
            actualTon = actualTotal.divide(volumeTotal, 4, RoundingMode.HALF_UP);
            planTon = planTotal.divide(volumeTotal, 4, RoundingMode.HALF_UP);
        } else {
            actualTon = BigDecimal.ZERO;
            planTon = BigDecimal.ZERO;
        }

        CwUnitCostBarResult res = new CwUnitCostBarResult();
        res.setUnit(getUnitDisplayName(unitKey));
        res.setActualTotalCost(actualTotal);
        res.setPlanTotalCost(planTotal);
        res.setActualTonCost(actualTon);
        res.setPlanTonCost(planTon);
        return res;
    }

    /**
     * 根据单位key返回显示名称
     */
    private String getUnitDisplayName(String key) {
        switch (key) {
            case "ckc":
                return "采矿场";
            case "ds":
                return "大山厂";
            case "sx":
                return "泗选厂";
            case "jw":
                return "精尾厂";
            default:
                return key;
        }
    }

    /**
     * 获取单位某个月份的实际/计划成本和处理量
     */
    private UnitMonthlyData fetchUnitMonthlyData(String unitKey, Date monthDate) {
        BigDecimal actual = BigDecimal.ZERO;
        BigDecimal plan = BigDecimal.ZERO;
        BigDecimal volume = BigDecimal.ZERO;

        // 实际总成本
        switch (unitKey) {
            case "ckc":
                actual = safe(ckcZhcbService.sumDrs(monthDate));
                plan = ckcZhcbService.sumBudgetMonth(monthDate);
                volume = sumStringMap(cwCllDataService.getMonthCllData(CwCllDataName.CBZL, monthDate));
                break;
            case "ds":
                actual = safe(dsZhcbService.sumDrs(monthDate));
                plan = dsZhcbService.sumBudgetMonth(monthDate);
                volume = sumStringMap(cwCllDataService.getMonthCllData(CwCllDataName.DSCLL, monthDate));
                break;
            case "sx":
                actual = safe(sxZhcbService.sumDrs(monthDate));
                plan = sxZhcbService.sumBudgetMonth(monthDate);
                volume = sumStringMap(cwCllDataService.getMonthCllData(CwCllDataName.SZCLL, monthDate));
                break;
            case "jw":
                actual = safe(jwZhcbService.sumDrs(monthDate));
                plan = jwZhcbService.sumBudgetMonth(monthDate);
                volume = sumStringMap(cwCllDataService.getMonthCllData("jw", monthDate));
                break;
            default:
                break;
        }

        return new UnitMonthlyData(actual, plan, volume);
    }

    /**
     * 统计月预算合计
     */
    private BigDecimal sumBudget(List<CwKrbRow> rows) {
        BigDecimal total = java.math.BigDecimal.ZERO;
        if (rows != null) {
            for (CwKrbRow row : rows) {
                total = total.add(nullSafe(row.getYys()));
            }
        }
        return total;
    }

    /**
     * 内部类：保存单月数据
     */
    private static class UnitMonthlyData {
        java.math.BigDecimal actual;
        java.math.BigDecimal plan;
        java.math.BigDecimal volume;
        UnitMonthlyData(java.math.BigDecimal a, java.math.BigDecimal p, java.math.BigDecimal v) {
            this.actual = a;
            this.plan = p;
            this.volume = v;
        }
    }

    @Override
    public org.jeecg.modules.cw.statistics.result.CwUnitCostDetailResult unitCostDetail(String unit, java.util.Date date, String dimension) {
        if (date == null) {
            date = new java.util.Date();
        }
        String unitKey = (unit == null ? "ckc" : unit.toLowerCase());
        String dim = (dimension == null ? "month" : dimension.toLowerCase());

        // 当前期数据累加器
        java.math.BigDecimal actualTotal = java.math.BigDecimal.ZERO;
        java.math.BigDecimal planTotal = java.math.BigDecimal.ZERO;
        java.math.BigDecimal volumeTotal = java.math.BigDecimal.ZERO;
        java.util.Map<String, org.jeecg.modules.cw.statistics.result.CwCostBreakdownResult> breakdownMap = new java.util.LinkedHashMap<>();

        if ("year".equals(dim)) {
            java.util.Date iter = cn.hutool.core.date.DateUtil.beginOfYear(date);
            while (!iter.after(date)) {
                MonthlyDetail md = fetchMonthlyDetail(unitKey, iter);
                actualTotal = actualTotal.add(md.actual);
                planTotal = planTotal.add(md.plan);
                volumeTotal = volumeTotal.add(md.volume);
                mergeBreakdown(breakdownMap, md.rows);
                iter = cn.hutool.core.date.DateUtil.offsetMonth(iter, 1);
            }
        } else {
            MonthlyDetail md = fetchMonthlyDetail(unitKey, date);
            actualTotal = md.actual;
            planTotal = md.plan;
            volumeTotal = md.volume;
            mergeBreakdown(breakdownMap, md.rows);
        }

        // 吨矿成本
        java.math.BigDecimal actualTon = java.math.BigDecimal.ZERO;
        java.math.BigDecimal planTon = java.math.BigDecimal.ZERO;
        if (volumeTotal != null && volumeTotal.compareTo(java.math.BigDecimal.ZERO) > 0) {
            actualTon = actualTotal.divide(volumeTotal, 4, java.math.RoundingMode.HALF_UP);
            planTon = planTotal.divide(volumeTotal, 4, java.math.RoundingMode.HALF_UP);
        }

        // 去年同期（month=去年同月； year=去年全年到同日）
        java.util.Date lastYearDate = cn.hutool.core.date.DateUtil.offset(date, cn.hutool.core.date.DateField.YEAR, -1);
        java.math.BigDecimal lastActualTotal;
        if ("year".equals(dim)) {
            // 去年全年累计同日
            java.math.BigDecimal sum = java.math.BigDecimal.ZERO;
            java.util.Date iter = cn.hutool.core.date.DateUtil.beginOfYear(lastYearDate);
            while (!iter.after(lastYearDate)) {
                sum = sum.add(fetchMonthlyDetail(unitKey, iter).actual);
                iter = cn.hutool.core.date.DateUtil.offsetMonth(iter, 1);
            }
            lastActualTotal = sum;
        } else {
            lastActualTotal = fetchMonthlyDetail(unitKey, lastYearDate).actual;
        }
        java.math.BigDecimal yoy = actualTotal.subtract(lastActualTotal);

        // 组装结果
        org.jeecg.modules.cw.statistics.result.CwUnitCostDetailResult res = new org.jeecg.modules.cw.statistics.result.CwUnitCostDetailResult();
        res.setUnit(getUnitDisplayName(unitKey));
        res.setDimension(dim);
        res.setPeriod("year".equals(dim) ? cn.hutool.core.date.DateUtil.format(date, "yyyy") : cn.hutool.core.date.DateUtil.format(date, "yyyy-MM"));
        res.setActualTotalCost(actualTotal);
        res.setPlanTotalCost(planTotal);
        res.setActualTonCost(actualTon);
        res.setPlanTonCost(planTon);
        res.setYoy(yoy);
        res.setBreakdown(new java.util.ArrayList<>(breakdownMap.values()));
        return res;
    }

    /** 合并一月的 breakdown 到累计map */
    private void mergeBreakdown(java.util.Map<String, org.jeecg.modules.cw.statistics.result.CwCostBreakdownResult> map, java.util.List<org.jeecg.modules.cw.krb.entity.CwKrbRow> rows) {
        if (rows == null) return;
        for (org.jeecg.modules.cw.krb.entity.CwKrbRow row : rows) {
            String type = row.getName();
            org.jeecg.modules.cw.statistics.result.CwCostBreakdownResult br = map.get(type);
            if (br == null) {
                br = new org.jeecg.modules.cw.statistics.result.CwCostBreakdownResult();
                br.setType(type);
                br.setName(row.getName());
                br.setActual(java.math.BigDecimal.ZERO);
                br.setPlan(java.math.BigDecimal.ZERO);
                map.put(type, br);
            }
            br.setActual(br.getActual().add(nullSafe(row.getRlj())));
            br.setPlan(br.getPlan().add(nullSafe(row.getYys())));
        }
    }

    /**
     * 月度明细
     */
    private MonthlyDetail fetchMonthlyDetail(String unitKey, java.util.Date monthDate) {
        java.math.BigDecimal actual = java.math.BigDecimal.ZERO;
        java.math.BigDecimal plan = java.math.BigDecimal.ZERO;
        java.math.BigDecimal volume = java.math.BigDecimal.ZERO;
        java.util.List<org.jeecg.modules.cw.krb.entity.CwKrbRow> rows = null;

        switch (unitKey) {
            case "ckc":
                actual = safe(ckcZhcbService.sumDrs(monthDate));
                rows = ckcZhcbService.sumByMonth(monthDate);
                plan = sumBudget(rows);
                volume = sumStringMap(cwCllDataService.getMonthCllData(org.jeecg.modules.cw.base.constants.CwCllDataName.CBZL, monthDate));
                break;
            case "ds":
                actual = safe(dsZhcbService.sumDrs(monthDate));
                rows = dsZhcbService.sumByMonth(monthDate);
                plan = sumBudget(rows);
                volume = sumStringMap(cwCllDataService.getMonthCllData(org.jeecg.modules.cw.base.constants.CwCllDataName.DSCLL, monthDate));
                break;
            case "sx":
                actual = safe(sxZhcbService.sumDrs(monthDate));
                rows = sxZhcbService.sumByMonth(monthDate);
                plan = sumBudget(rows);
                volume = sumStringMap(cwCllDataService.getMonthCllData(org.jeecg.modules.cw.base.constants.CwCllDataName.SZCLL, monthDate));
                break;
            case "jw":
                actual = safe(jwZhcbService.sumDrs(monthDate));
                rows = jwZhcbService.sumByMonth(monthDate);
                plan = sumBudget(rows);
                volume = sumStringMap(cwCllDataService.getMonthCllData("jw", monthDate));
                break;
            default:
                break;
        }
        return new MonthlyDetail(actual, plan, volume, rows);
    }

    private static class MonthlyDetail {
        java.math.BigDecimal actual;
        java.math.BigDecimal plan;
        java.math.BigDecimal volume;
        java.util.List<org.jeecg.modules.cw.krb.entity.CwKrbRow> rows;
        MonthlyDetail(java.math.BigDecimal a, java.math.BigDecimal p, java.math.BigDecimal v, java.util.List<org.jeecg.modules.cw.krb.entity.CwKrbRow> r) {
            actual = a; plan = p; volume = v; rows = r;
        }
    }
}
